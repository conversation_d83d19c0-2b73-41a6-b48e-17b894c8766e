# Spring Expression Language (SpEL) 演示项目

## 项目概述

本项目是一个基于Spring Boot 3.x的控制台应用程序，专门用于演示Spring Expression Language (SpEL)的各种功能和用法。通过丰富的示例代码和详细的注释，帮助开发者深入理解和掌握SpEL的强大功能。

## 功能特性

### 🎯 SpEL功能演示

1. **基本属性访问**
   - 简单属性访问：`user.name`
   - 嵌套属性访问：`user.address.city.name`
   - 安全导航操作符：`user.address?.city?.name`

2. **方法调用**
   - 简单方法调用：`user.name.toUpperCase()`
   - 带参数的方法调用：`user.name.substring(0, 3)`
   - 链式方法调用：`user.name.toLowerCase().replace(' ', '_')`

3. **集合操作**
   - 集合大小：`user.roles.size()`
   - 集合过滤：`user.roles.?[length() > 4]`
   - 集合投影：`user.roles.![toUpperCase()]`
   - 集合选择：`user.roles.^[true]` (第一个), `user.roles.$[true]` (最后一个)

4. **逻辑表达式**
   - 简单逻辑：`user.age > 18 and user.active == true`
   - 复杂逻辑：`user.age >= 18 and user.active and (user.roles.contains('USER') or user.roles.contains('ADMIN'))`

5. **条件运算符**
   - 三元运算符：`user.age >= 18 ? '成年人' : '未成年人'`
   - Elvis运算符：`user.nickname ?: '未设置昵称'`

6. **变量和上下文**
   - 变量定义和使用：`#minAge`, `#maxAge`
   - 复杂变量表达式：`user.age >= #minAge ? '可以工作' : ('还需等待 ' + (#minAge - user.age) + ' 年')`

### 🏗️ 项目结构

```
src/main/java/com/example/speldemo/
├── SpelDemoApplication.java          # 主应用程序类
├── model/                            # 领域模型
│   ├── User.java                     # 用户实体
│   ├── Address.java                  # 地址实体
│   ├── City.java                     # 城市实体
│   └── Country.java                  # 国家实体
├── service/                          # 服务层
│   └── SpELDemoService.java          # SpEL演示服务
└── factory/                          # 数据工厂
    └── UserDataFactory.java          # 用户数据工厂
```

### 📊 领域模型设计

项目采用了层次化的领域模型设计：

- **User** (用户)
  - 基本属性：id, name, age, active, registrationDate
  - 集合属性：roles (List), permissions (Set)
  - 嵌套对象：address
  - 可选属性：nickname, email

- **Address** (地址)
  - 属性：street, zipCode, type
  - 嵌套对象：city

- **City** (城市)
  - 属性：name, population, capital
  - 嵌套对象：country

- **Country** (国家)
  - 属性：code, name, population, developed

## 环境要求

- **Java**: 17 或更高版本
- **Maven**: 3.6 或更高版本
- **IDE**: IntelliJ IDEA (推荐) 或其他支持Java的IDE

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd spel-demo
```

### 2. 导入到IntelliJ IDEA

1. 打开IntelliJ IDEA
2. 选择 `File` -> `Open`
3. 选择项目根目录
4. 等待Maven依赖下载完成

### 3. 运行项目

#### 方式一：通过IDE运行
1. 找到 `SpelDemoApplication.java` 文件
2. 右键点击文件
3. 选择 `Run 'SpelDemoApplication'`

#### 方式二：通过Maven命令行运行
```bash
mvn spring-boot:run
```

#### 方式三：打包后运行
```bash
mvn clean package
java -jar target/spel-demo-1.0.0.jar
```

## 示例输出

运行程序后，您将看到类似以下的输出：

```
========================================
Spring Expression Language (SpEL) 演示
========================================

============================================================
演示用户 1 - 张三
============================================================

=== 用户基本信息 ===
ID: 1
姓名: 张三
昵称: 小张
年龄: 28
邮箱: <EMAIL>
激活状态: true
注册时间: 2023-06-15 10:30:00
角色: [USER, ADMIN, MODERATOR]
权限: [READ, WRITE, DELETE, ADMIN_PANEL, USER_MANAGEMENT]
地址: 朝阳区建国门外大街1号, 北京, 中国 100001
国家: 中国 (CN) - 人口: 1,400,000,000
用户状态: 管理员

=== 基本属性访问演示 ===
用户姓名: 张三
用户所在城市: 北京
用户所在国家: 中国
国家代码（安全导航）: CN

=== 方法调用演示 ===
大写姓名: 张三
姓名前3个字符: 张三
处理后的姓名: 张三
是否成年: true
完整地址: 朝阳区建国门外大街1号, 北京, 中国 100001

... (更多演示输出)
```

## 学习要点

### 1. SpEL表达式语法
- 属性访问：使用点号 `.` 访问对象属性
- 方法调用：使用括号 `()` 调用方法
- 安全导航：使用 `?.` 避免空指针异常
- 变量引用：使用 `#` 前缀引用变量

### 2. 集合操作符
- `?[]`：选择操作符，过滤集合元素
- `![]`：投影操作符，转换集合元素
- `^[]`：选择第一个匹配的元素
- `$[]`：选择最后一个匹配的元素

### 3. 条件运算符
- `condition ? trueValue : falseValue`：三元运算符
- `value ?: defaultValue`：Elvis运算符（空值合并）

### 4. 逻辑运算符
- `and`, `or`, `not`：逻辑运算
- `==`, `!=`, `<`, `>`, `<=`, `>=`：比较运算

## 扩展学习

### 自定义函数
您可以通过注册自定义函数来扩展SpEL的功能：

```java
context.registerFunction("reverse", 
    StringUtils.class.getDeclaredMethod("reverse", String.class));
```

### 类型转换
SpEL支持自动类型转换，也可以显式指定类型：

```java
Expression expr = parser.parseExpression("'123' + 456");
String result = expr.getValue(String.class); // "123456"
```

### 安全性考虑
在生产环境中使用SpEL时，请注意：
- 避免执行用户输入的表达式
- 使用 `SimpleEvaluationContext` 替代 `StandardEvaluationContext` 以提高安全性
- 限制可访问的类和方法

## 贡献指南

欢迎提交Issue和Pull Request来改进这个演示项目！

## 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。

## 相关资源

- [Spring Framework官方文档 - SpEL](https://docs.spring.io/spring-framework/docs/current/reference/html/core.html#expressions)
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [Maven官方文档](https://maven.apache.org/guides/)
