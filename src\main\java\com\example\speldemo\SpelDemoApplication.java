package com.example.speldemo;

import com.example.speldemo.factory.UserDataFactory;
import com.example.speldemo.model.User;
import com.example.speldemo.service.SpELDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.List;

/**
 * Spring Expression Language (SpEL) 演示应用程序
 * 
 * 本应用程序演示了SpEL的各种功能和用法，包括：
 * - 基本属性访问
 * - 方法调用
 * - 集合操作
 * - 逻辑表达式
 * - 三元运算符和Elvis运算符
 * - 变量使用
 * - 高级表达式应用
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Slf4j
@RequiredArgsConstructor
@SpringBootApplication
public class SpelDemoApplication implements CommandLineRunner {
    
    private final SpELDemoService spelDemoService;
    private final UserDataFactory userDataFactory;
    
    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        log.info("启动 Spring Expression Language (SpEL) 演示应用程序...");
        SpringApplication.run(SpelDemoApplication.class, args);
    }
    
    /**
     * 应用程序启动后执行的逻辑
     * 
     * @param args 命令行参数
     */
    @Override
    public void run(String... args) {
        log.info("========================================");
        log.info("Spring Expression Language (SpEL) 演示");
        log.info("========================================");
        
        // 创建示例数据
        List<User> users = userDataFactory.createAllSampleUsers();
        
        // 对每个用户执行SpEL演示
        for (int i = 0; i < users.size(); i++) {
            User user = users.get(i);
            
            log.info("\n" + "=".repeat(60));
            log.info("演示用户 {} - {}", i + 1, user.getName());
            log.info("=".repeat(60));
            
            // 显示用户基本信息
            displayUserInfo(user);
            
            // 执行各种SpEL演示
            spelDemoService.demonstrateBasicPropertyAccess(user);
            spelDemoService.demonstrateMethodCalls(user);
            spelDemoService.demonstrateCollectionOperations(user);
            spelDemoService.demonstrateLogicalExpressions(user);
            spelDemoService.demonstrateTernaryAndElvisOperators(user);
            spelDemoService.demonstrateVariablesAndContext(user);
            spelDemoService.demonstrateAdvancedExpressions(user);
        }
        
        log.info("========================================");
        log.info("SpEL 演示完成！");
        log.info("========================================");
    }
    
    /**
     * 显示用户基本信息
     * 
     * @param user 用户对象
     */
    private void displayUserInfo(User user) {
        log.info("=== 用户基本信息 ===");
        log.info("ID: {}", user.getId());
        log.info("姓名: {}", user.getName());
        log.info("昵称: {}", user.getNickname());
        log.info("年龄: {}", user.getAge());
        log.info("邮箱: {}", user.getEmail());
        log.info("激活状态: {}", user.isActive());
        log.info("注册时间: {}", user.getFormattedRegistrationDate());
        log.info("角色: {}", user.getRoles());
        log.info("权限: {}", user.getPermissions());
        
        if (user.getAddress() != null) {
            log.info("地址: {}", user.getAddress().getFullAddress());
            if (user.getAddress().getCity() != null && user.getAddress().getCity().getCountry() != null) {
                log.info("国家: {}", user.getAddress().getCity().getCountry().getDescription());
            }
        }
        
        log.info("用户状态: {}", user.getStatusDescription());
        log.info("");
    }
}
