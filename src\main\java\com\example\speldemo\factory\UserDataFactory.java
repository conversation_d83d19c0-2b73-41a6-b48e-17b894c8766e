package com.example.speldemo.factory;

import com.example.speldemo.model.Address;
import com.example.speldemo.model.City;
import com.example.speldemo.model.Country;
import com.example.speldemo.model.User;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户数据工厂类
 * 用于创建演示用的示例数据
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Component
public class UserDataFactory {
    
    /**
     * 创建示例用户数据
     * 
     * @return 包含完整信息的用户对象
     */
    public User createSampleUser() {
        // 创建国家
        Country china = new Country("CN", "中国", 1_400_000_000L, false);
        
        // 创建城市
        City beijing = new City("北京", 21_000_000, true, china);
        
        // 创建地址
        Address address = new Address("朝阳区建国门外大街1号", "100001", beijing, "工作地址");
        
        // 创建角色列表
        List<String> roles = Arrays.asList("USER", "ADMIN", "MODERATOR");
        
        // 创建权限集合
        Set<String> permissions = new HashSet<>(Arrays.asList(
            "READ", "WRITE", "DELETE", "ADMIN_PANEL", "USER_MANAGEMENT"
        ));
        
        // 创建用户
        User user = new User();
        user.setId(1L);
        user.setName("张三");
        user.setAge(28);
        user.setRoles(roles);
        user.setPermissions(permissions);
        user.setActive(true);
        user.setRegistrationDate(LocalDateTime.of(2023, 6, 15, 10, 30, 0));
        user.setAddress(address);
        user.setNickname("小张");
        user.setEmail("<EMAIL>");
        
        return user;
    }
    
    /**
     * 创建未成年用户示例
     * 
     * @return 未成年用户对象
     */
    public User createMinorUser() {
        // 创建国家
        Country usa = new Country("US", "美国", 330_000_000L, true);
        
        // 创建城市
        City newYork = new City("纽约", 8_400_000, false, usa);
        
        // 创建地址
        Address address = new Address("第五大道123号", "10001", newYork, "家庭地址");
        
        // 创建角色列表（未成年用户权限较少）
        List<String> roles = Arrays.asList("GUEST", "STUDENT");
        
        // 创建权限集合
        Set<String> permissions = new HashSet<>(Arrays.asList("READ", "COMMENT"));
        
        // 创建用户
        User user = new User();
        user.setId(2L);
        user.setName("李小明");
        user.setAge(16);
        user.setRoles(roles);
        user.setPermissions(permissions);
        user.setActive(true);
        user.setRegistrationDate(LocalDateTime.of(2024, 1, 10, 14, 20, 0));
        user.setAddress(address);
        user.setNickname(null); // 没有昵称，用于演示Elvis运算符
        user.setEmail(null); // 没有邮箱，用于演示空值处理
        
        return user;
    }
    
    /**
     * 创建未激活用户示例
     * 
     * @return 未激活用户对象
     */
    public User createInactiveUser() {
        // 创建国家
        Country japan = new Country("JP", "日本", 125_000_000L, true);
        
        // 创建城市
        City tokyo = new City("东京", 13_900_000, true, japan);
        
        // 创建地址
        Address address = new Address("涩谷区道玄坂2-1", "150-0043", tokyo, "临时地址");
        
        // 创建角色列表
        List<String> roles = Arrays.asList("USER");
        
        // 创建权限集合
        Set<String> permissions = new HashSet<>(Arrays.asList("READ"));
        
        // 创建用户
        User user = new User();
        user.setId(3L);
        user.setName("田中太郎");
        user.setAge(35);
        user.setRoles(roles);
        user.setPermissions(permissions);
        user.setActive(false); // 未激活状态
        user.setRegistrationDate(LocalDateTime.of(2024, 2, 28, 9, 15, 0));
        user.setAddress(address);
        user.setNickname("太郎君");
        user.setEmail("<EMAIL>");
        
        return user;
    }
    
    /**
     * 创建所有示例用户
     * 
     * @return 用户列表
     */
    public List<User> createAllSampleUsers() {
        return Arrays.asList(
            createSampleUser(),
            createMinorUser(),
            createInactiveUser()
        );
    }
}
