package com.example.speldemo.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地址实体类
 * 用于演示SpEL中的嵌套对象访问
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Address {
    
    /**
     * 街道地址
     */
    private String street;
    
    /**
     * 邮政编码
     */
    private String zipCode;
    
    /**
     * 所在城市
     */
    private City city;
    
    /**
     * 地址类型（如：家庭地址、工作地址等）
     */
    private String type;
    
    /**
     * 获取完整的地址字符串
     * 
     * @return 格式化的完整地址
     */
    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (street != null && !street.trim().isEmpty()) {
            sb.append(street);
        }
        if (city != null) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(city.getFullName());
        }
        if (zipCode != null && !zipCode.trim().isEmpty()) {
            if (sb.length() > 0) {
                sb.append(" ");
            }
            sb.append(zipCode);
        }
        return sb.toString();
    }
    
    /**
     * 判断是否为国际地址（非本国地址）
     * 
     * @param homeCountryCode 本国国家代码
     * @return true如果是国际地址，否则false
     */
    public boolean isInternational(String homeCountryCode) {
        return city != null && 
               city.getCountry() != null && 
               !homeCountryCode.equals(city.getCountry().getCode());
    }
}
