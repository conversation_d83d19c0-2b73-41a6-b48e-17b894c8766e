package com.example.speldemo.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 城市实体类
 * 用于演示SpEL中的嵌套对象访问
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class City {
    
    /**
     * 城市名称
     */
    private String name;
    
    /**
     * 城市人口
     */
    private Integer population;
    
    /**
     * 是否为首都
     */
    private boolean capital;
    
    /**
     * 所属国家
     */
    private Country country;
    
    /**
     * 获取城市的完整名称（包含国家信息）
     * 
     * @return 完整的城市名称
     */
    public String getFullName() {
        if (country != null) {
            return String.format("%s, %s", name, country.getName());
        }
        return name;
    }
    
    /**
     * 判断是否为大城市（人口超过100万）
     * 
     * @return true如果人口超过100万，否则false
     */
    public boolean isMegaCity() {
        return population != null && population > 1_000_000;
    }
    
    /**
     * 获取城市类型描述
     * 
     * @return 城市类型字符串
     */
    public String getCityType() {
        if (capital) {
            return "首都";
        } else if (isMegaCity()) {
            return "大城市";
        } else {
            return "普通城市";
        }
    }
}
