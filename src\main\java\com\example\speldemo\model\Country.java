package com.example.speldemo.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 国家实体类
 * 用于演示SpEL中的嵌套对象访问
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Country {
    
    /**
     * 国家代码（如：CN, US, JP）
     */
    private String code;
    
    /**
     * 国家名称
     */
    private String name;
    
    /**
     * 国家人口数量
     */
    private Long population;
    
    /**
     * 是否为发达国家
     */
    private boolean developed;
    
    /**
     * 获取国家的简短描述
     * 
     * @return 国家描述字符串
     */
    public String getDescription() {
        return String.format("%s (%s) - 人口: %,d", name, code, population);
    }
    
    /**
     * 判断是否为人口大国（人口超过1亿）
     * 
     * @return true如果人口超过1亿，否则false
     */
    public boolean isPopulous() {
        return population != null && population > 100_000_000L;
    }
}
