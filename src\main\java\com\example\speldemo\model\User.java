package com.example.speldemo.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

/**
 * 用户实体类
 * 用于演示SpEL的各种功能，包括属性访问、方法调用、集合操作等
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户姓名
     */
    private String name;
    
    /**
     * 用户年龄
     */
    private Integer age;
    
    /**
     * 用户角色列表
     */
    private List<String> roles;
    
    /**
     * 用户权限集合
     */
    private Set<String> permissions;
    
    /**
     * 用户是否激活
     */
    private boolean active;
    
    /**
     * 注册时间
     */
    private LocalDateTime registrationDate;
    
    /**
     * 用户地址
     */
    private Address address;
    
    /**
     * 用户昵称（可选）
     */
    private String nickname;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 获取用户的显示名称
     * 优先使用昵称，如果没有昵称则使用真实姓名
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return nickname != null && !nickname.trim().isEmpty() ? nickname : name;
    }
    
    /**
     * 判断用户是否为成年人
     * 
     * @return true如果年龄>=18，否则false
     */
    public boolean isAdult() {
        return age != null && age >= 18;
    }
    
    /**
     * 判断用户是否为管理员
     * 
     * @return true如果拥有管理员角色，否则false
     */
    public boolean isAdmin() {
        return roles != null && roles.contains("ADMIN");
    }
    
    /**
     * 获取用户状态描述
     * 
     * @return 状态描述字符串
     */
    public String getStatusDescription() {
        if (!active) {
            return "未激活";
        }
        if (isAdmin()) {
            return "管理员";
        }
        if (isAdult()) {
            return "普通用户";
        }
        return "未成年用户";
    }
    
    /**
     * 获取格式化的注册时间
     * 
     * @return 格式化的时间字符串
     */
    public String getFormattedRegistrationDate() {
        if (registrationDate == null) {
            return "未知";
        }
        return registrationDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 检查用户是否拥有指定权限
     * 
     * @param permission 权限名称
     * @return true如果拥有该权限，否则false
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }
    
    /**
     * 获取用户的角色数量
     * 
     * @return 角色数量
     */
    public int getRoleCount() {
        return roles != null ? roles.size() : 0;
    }
}
