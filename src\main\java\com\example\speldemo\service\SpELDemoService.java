package com.example.speldemo.service;

import com.example.speldemo.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Spring Expression Language (SpEL) 演示服务类
 * 
 * 本类演示了SpEL的各种功能，包括：
 * 1. 基本属性访问
 * 2. 方法调用
 * 3. 集合操作
 * 4. 逻辑表达式
 * 5. 三元运算符
 * 6. Elvis运算符
 * 7. 嵌套对象访问
 * 8. 变量使用
 * 
 * <AUTHOR> Demo
 * @version 1.0
 */
@Slf4j
@Service
public class SpELDemoService {
    
    /**
     * SpEL表达式解析器
     */
    private final ExpressionParser parser = new SpelExpressionParser();
    
    /**
     * 演示基本属性访问表达式
     * 
     * @param user 用户对象
     */
    public void demonstrateBasicPropertyAccess(User user) {
        log.info("=== 基本属性访问演示 ===");
        
        // 创建评估上下文并设置根对象
        StandardEvaluationContext context = new StandardEvaluationContext(user);
        
        // 1. 简单属性访问
        Expression nameExpr = parser.parseExpression("name");
        String name = nameExpr.getValue(context, String.class);
        log.info("用户姓名: {}", name);
        
        // 2. 嵌套属性访问
        Expression cityNameExpr = parser.parseExpression("address.city.name");
        String cityName = cityNameExpr.getValue(context, String.class);
        log.info("用户所在城市: {}", cityName);
        
        // 3. 深层嵌套属性访问
        Expression countryNameExpr = parser.parseExpression("address.city.country.name");
        String countryName = countryNameExpr.getValue(context, String.class);
        log.info("用户所在国家: {}", countryName);
        
        // 4. 安全导航操作符（避免空指针异常）
        Expression safeNavExpr = parser.parseExpression("address?.city?.country?.code");
        String countryCode = safeNavExpr.getValue(context, String.class);
        log.info("国家代码（安全导航）: {}", countryCode);
        
        log.info("");
    }
    
    /**
     * 演示方法调用表达式
     * 
     * @param user 用户对象
     */
    public void demonstrateMethodCalls(User user) {
        log.info("=== 方法调用演示 ===");
        
        StandardEvaluationContext context = new StandardEvaluationContext(user);
        
        // 1. 简单方法调用
        Expression upperCaseExpr = parser.parseExpression("name.toUpperCase()");
        String upperCaseName = upperCaseExpr.getValue(context, String.class);
        log.info("大写姓名: {}", upperCaseName);
        
        // 2. 带参数的方法调用
        Expression substringExpr = parser.parseExpression("name.substring(0, 3)");
        String substring = substringExpr.getValue(context, String.class);
        log.info("姓名前3个字符: {}", substring);
        
        // 3. 链式方法调用
        Expression chainExpr = parser.parseExpression("name.toLowerCase().replace(' ', '_')");
        String processedName = chainExpr.getValue(context, String.class);
        log.info("处理后的姓名: {}", processedName);
        
        // 4. 自定义方法调用
        Expression isAdultExpr = parser.parseExpression("isAdult()");
        Boolean isAdult = isAdultExpr.getValue(context, Boolean.class);
        log.info("是否成年: {}", isAdult);
        
        // 5. 嵌套对象方法调用
        Expression fullAddressExpr = parser.parseExpression("address.getFullAddress()");
        String fullAddress = fullAddressExpr.getValue(context, String.class);
        log.info("完整地址: {}", fullAddress);
        
        log.info("");
    }

    /**
     * 演示集合操作表达式
     *
     * @param user 用户对象
     */
    public void demonstrateCollectionOperations(User user) {
        log.info("=== 集合操作演示 ===");

        StandardEvaluationContext context = new StandardEvaluationContext(user);

        // 1. 集合大小
        Expression rolesSizeExpr = parser.parseExpression("roles.size()");
        Integer rolesSize = rolesSizeExpr.getValue(context, Integer.class);
        log.info("角色数量: {}", rolesSize);

        // 2. 集合包含检查
        Expression containsAdminExpr = parser.parseExpression("roles.contains('ADMIN')");
        Boolean containsAdmin = containsAdminExpr.getValue(context, Boolean.class);
        log.info("是否包含ADMIN角色: {}", containsAdmin);

        // 3. 集合过滤 - 选择操作（?[]）
        Expression longRolesExpr = parser.parseExpression("roles.?[length() > 4]");
        List<?> longRoles = longRolesExpr.getValue(context, List.class);
        log.info("长度大于4的角色: {}", longRoles);

        // 4. 集合投影 - 映射操作（![]）
        Expression upperRolesExpr = parser.parseExpression("roles.![toUpperCase()]");
        List<?> upperRoles = upperRolesExpr.getValue(context, List.class);
        log.info("大写角色列表: {}", upperRoles);

        // 5. 集合的第一个和最后一个元素
        Expression firstRoleExpr = parser.parseExpression("roles.?[true].^[true]");
        String firstRole = firstRoleExpr.getValue(context, String.class);
        log.info("第一个角色: {}", firstRole);

        Expression lastRoleExpr = parser.parseExpression("roles.?[true].$[true]");
        String lastRole = lastRoleExpr.getValue(context, String.class);
        log.info("最后一个角色: {}", lastRole);

        // 6. 权限集合操作
        Expression hasReadPermExpr = parser.parseExpression("permissions.contains('READ')");
        Boolean hasReadPerm = hasReadPermExpr.getValue(context, Boolean.class);
        log.info("是否有READ权限: {}", hasReadPerm);

        log.info("");
    }

    /**
     * 演示逻辑表达式和条件运算
     *
     * @param user 用户对象
     */
    public void demonstrateLogicalExpressions(User user) {
        log.info("=== 逻辑表达式演示 ===");

        StandardEvaluationContext context = new StandardEvaluationContext(user);

        // 1. 简单逻辑表达式
        Expression ageCheckExpr = parser.parseExpression("age > 18 and active == true");
        Boolean ageCheck = ageCheckExpr.getValue(context, Boolean.class);
        log.info("年龄大于18且账户激活: {}", ageCheck);

        // 2. 复杂逻辑表达式
        Expression complexExpr = parser.parseExpression(
            "age >= 18 and active and (roles.contains('USER') or roles.contains('ADMIN'))");
        Boolean complexResult = complexExpr.getValue(context, Boolean.class);
        log.info("复杂条件检查: {}", complexResult);

        // 3. 数值比较
        Expression ageRangeExpr = parser.parseExpression("age >= 18 and age <= 65");
        Boolean inAgeRange = ageRangeExpr.getValue(context, Boolean.class);
        log.info("年龄在工作范围内: {}", inAgeRange);

        // 4. 字符串比较
        Expression nameCheckExpr = parser.parseExpression("name.length() > 2 and name.startsWith('张')");
        Boolean nameCheck = nameCheckExpr.getValue(context, Boolean.class);
        log.info("姓名长度大于2且姓张: {}", nameCheck);

        // 5. 空值检查
        Expression nullCheckExpr = parser.parseExpression("nickname != null and nickname.length() > 0");
        Boolean hasNickname = nullCheckExpr.getValue(context, Boolean.class);
        log.info("有昵称: {}", hasNickname);

        log.info("");
    }

    /**
     * 演示三元运算符和Elvis运算符
     *
     * @param user 用户对象
     */
    public void demonstrateTernaryAndElvisOperators(User user) {
        log.info("=== 三元运算符和Elvis运算符演示 ===");

        StandardEvaluationContext context = new StandardEvaluationContext(user);

        // 1. 三元运算符 - 年龄判断
        Expression ageStatusExpr = parser.parseExpression("age >= 18 ? '成年人' : '未成年人'");
        String ageStatus = ageStatusExpr.getValue(context, String.class);
        log.info("年龄状态: {}", ageStatus);

        // 2. 三元运算符 - 账户状态
        Expression accountStatusExpr = parser.parseExpression("active ? '激活' : '未激活'");
        String accountStatus = accountStatusExpr.getValue(context, String.class);
        log.info("账户状态: {}", accountStatus);

        // 3. 嵌套三元运算符
        Expression userTypeExpr = parser.parseExpression(
            "roles.contains('ADMIN') ? '管理员' : (roles.contains('USER') ? '普通用户' : '访客')");
        String userType = userTypeExpr.getValue(context, String.class);
        log.info("用户类型: {}", userType);

        // 4. Elvis运算符 - 空值处理
        Expression nicknameExpr = parser.parseExpression("nickname ?: '未设置昵称'");
        String displayNickname = nicknameExpr.getValue(context, String.class);
        log.info("显示昵称: {}", displayNickname);

        // 5. Elvis运算符 - 复杂表达式
        Expression emailDisplayExpr = parser.parseExpression("email ?: '邮箱未设置'");
        String emailDisplay = emailDisplayExpr.getValue(context, String.class);
        log.info("邮箱显示: {}", emailDisplay);

        // 6. 组合使用
        Expression combinedExpr = parser.parseExpression(
            "(nickname ?: name) + ' (' + (age >= 18 ? '成年' : '未成年') + ')'");
        String combined = combinedExpr.getValue(context, String.class);
        log.info("组合显示: {}", combined);

        log.info("");
    }

    /**
     * 演示变量使用和上下文操作
     *
     * @param user 用户对象
     */
    public void demonstrateVariablesAndContext(User user) {
        log.info("=== 变量和上下文演示 ===");

        // 创建评估上下文
        StandardEvaluationContext context = new StandardEvaluationContext(user);

        // 设置变量
        context.setVariable("minAge", 18);
        context.setVariable("maxAge", 65);
        context.setVariable("adminRole", "ADMIN");
        context.setVariable("currentYear", 2024);

        // 1. 使用变量进行比较
        Expression ageRangeExpr = parser.parseExpression("age >= #minAge and age <= #maxAge");
        Boolean inWorkingAge = ageRangeExpr.getValue(context, Boolean.class);
        log.info("在工作年龄范围内: {}", inWorkingAge);

        // 2. 变量在集合操作中的使用
        Expression hasAdminRoleExpr = parser.parseExpression("roles.contains(#adminRole)");
        Boolean hasAdminRole = hasAdminRoleExpr.getValue(context, Boolean.class);
        log.info("拥有管理员角色: {}", hasAdminRole);

        // 3. 计算表达式中使用变量
        Expression birthYearExpr = parser.parseExpression("#currentYear - age");
        Integer birthYear = birthYearExpr.getValue(context, Integer.class);
        log.info("估算出生年份: {}", birthYear);

        // 4. 复杂变量表达式
        Expression complexVarExpr = parser.parseExpression(
            "age >= #minAge ? '可以工作' : ('还需等待 ' + (#minAge - age) + ' 年')");
        String workStatus = complexVarExpr.getValue(context, String.class);
        log.info("工作状态: {}", workStatus);

        log.info("");
    }

    /**
     * 演示综合SpEL表达式应用
     *
     * @param user 用户对象
     */
    public void demonstrateAdvancedExpressions(User user) {
        log.info("=== 高级表达式演示 ===");

        StandardEvaluationContext context = new StandardEvaluationContext(user);
        context.setVariable("threshold", 3);

        // 1. 复杂的用户权限检查
        Expression permissionExpr = parser.parseExpression(
            "active and age >= 18 and roles.size() >= #threshold and permissions.contains('READ')");
        Boolean hasFullAccess = permissionExpr.getValue(context, Boolean.class);
        log.info("拥有完整访问权限: {}", hasFullAccess);

        // 2. 地址信息综合处理
        Expression addressInfoExpr = parser.parseExpression(
            "address != null ? (address.city.name + ', ' + address.city.country.name) : '地址未设置'");
        String addressInfo = addressInfoExpr.getValue(context, String.class);
        log.info("地址信息: {}", addressInfo);

        // 3. 用户等级计算
        Expression userLevelExpr = parser.parseExpression(
            "roles.contains('ADMIN') ? 'VIP' : (permissions.size() > 3 ? 'Premium' : 'Basic')");
        String userLevel = userLevelExpr.getValue(context, String.class);
        log.info("用户等级: {}", userLevel);

        // 4. 动态消息生成
        Expression messageExpr = parser.parseExpression(
            "'欢迎 ' + (nickname ?: name) + '！您是' + " +
            "(age >= 18 ? '成年' : '未成年') + '用户，账户状态：' + (active ? '正常' : '未激活')");
        String welcomeMessage = messageExpr.getValue(context, String.class);
        log.info("欢迎消息: {}", welcomeMessage);

        log.info("");
    }
}
