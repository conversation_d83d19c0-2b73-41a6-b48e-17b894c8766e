# Spring Boot 应用程序配置
spring:
  application:
    name: spel-demo
  
  # 日志配置
  output:
    ansi:
      enabled: always

# 日志配置
logging:
  level:
    com.example.speldemo: INFO
    org.springframework: WARN
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 应用程序信息
info:
  app:
    name: Spring Expression Language Demo
    description: 演示 Spring Expression Language (SpEL) 各种功能的示例应用程序
    version: 1.0.0
    author: SpEL Demo Team
